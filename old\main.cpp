#include "BluetoothSerial.h"
#include "bluetooth_protocol.h"
#include <ESP32-HUB75-MatrixPanel-I2S-DMA.h>
#include "DisplayDriver.h"
#include "LEDController.h"
#include "config.h"
#include "FontData.h"

String device_name = "ESP32-BT-Slave";
BluetoothProtocolParser btParser; // 蓝牙协议解析器
BluetoothFrame currentFrame;      // 当前解析的帧

// Check if Bluetooth is available
#if !defined(CONFIG_BT_ENABLED) || !defined(CONFIG_BLUEDROID_ENABLED)
#error Bluetooth is not enabled! Please run `make menuconfig` to and enable it
#endif

// Check Serial Port Profile
#if !defined(CONFIG_BT_SPP_ENABLED)
#error Serial Port Profile for Bluetooth is not available or not enabled. It is only available for the ESP32 chip.
#endif

BluetoothSerial SerialBT;

// 函数声明
void handleParseResult(ParseResult result);
void processBluetoothCommand(const BluetoothFrame &frame);
void handleTextCommand(const BluetoothFrame &frame);
void handleUpperTextCommand(const uint16_t *fontData, int charCount); // 独立处理上半屏
void handleLowerTextCommand(const uint16_t *fontData, int charCount); // 独立处理下半屏
void handleColorCommand(const BluetoothFrame &frame);
void handleBorderCommand(const BluetoothFrame &frame);
void handleSpecificColorCommand(const BluetoothFrame &frame);
void handleRandomColorCommand(const BluetoothFrame &frame);

void setup()
{
    Serial.begin(115200);

    // 初始化显示硬件
    if (!initializeDisplay())
    {
        Serial.println("显示硬件初始化失败！");
        return;
    }

    Serial.println("=== ESP32 LED屏控制器 ===");
    Serial.println("硬件初始化完成");

    // 启动蓝牙串口
    SerialBT.begin(device_name);
    Serial.printf("蓝牙设备已启动，设备名: %s\n", device_name.c_str());
    Serial.println("可以配对连接了");

    // 根据字体大小设置初始点阵数据（使用FontData中的示例数据）
    if (currentFontSize == BT_FONT_32x32)
    {
        handleFullScreenTextCommand(full_text, getFullTextCharCount());
    }
    else
    {
        handleTextCommand(upper_text, getUpperTextCharCount(), lower_text, getLowerTextCharCount());
    }

    // 初始化新增颜色系统
    initSpecificColorSystem();
    initRandomColorSystem();
}

void loop()
{
    while (SerialBT.available())
    {
        uint8_t receivedByte = SerialBT.read();
        ParseResult result = btParser.parseByte(receivedByte, currentFrame);

        if (result != ParseResult::NEED_MORE_DATA)
        {
            handleParseResult(result);
        }
    }

    updateAllEffects();   // 更新所有特效
    updateBrightness();   // 更新亮度设置
    updateColors();       // 更新颜色状态
    // updateRandomColors(); // 更新随机颜色（暂时抛弃）
    updateTextDisplay();  // 更新文本显示
}

// 处理解析结果
void handleParseResult(ParseResult result)
{
    switch (result)
    {
    case ParseResult::FRAME_COMPLETE:
        Serial.printf("收到完整帧 - 命令: 0x%02X, 数据长度: %d\n",
                      currentFrame.command, currentFrame.dataLength);

        // 打印完整帧数据（十六进制格式）
        Serial.print("完整帧数据: AA 55 ");
        Serial.printf("%02X ", currentFrame.command);
        Serial.printf("%02X %02X ", (currentFrame.dataLength >> 8) & 0xFF, currentFrame.dataLength & 0xFF);
        if (currentFrame.dataLength > 0 && currentFrame.data != nullptr)
        {
            for (int i = 0; i < currentFrame.dataLength; i++)
            {
                Serial.printf("%02X ", currentFrame.data[i]);
            }
        }
        Serial.println("0D 0A");

        processBluetoothCommand(currentFrame);
        btParser.reset(); // 重置解析器准备下一帧
        break;

    case ParseResult::FRAME_ERROR:
        Serial.println("错误: 帧格式错误");
        break;

    case ParseResult::INVALID_COMMAND:
        Serial.println("错误: 无效命令");
        break;

    case ParseResult::DATA_TOO_LONG:
        Serial.println("错误: 数据过长");
        break;

    case ParseResult::NEED_MORE_DATA:
        // 继续等待更多数据，无需处理
        break;

    default:
        Serial.printf("未知解析结果: %d\n", (int)result);
        break;
    }
}

// 处理蓝牙命令
void processBluetoothCommand(const BluetoothFrame &frame)
{
    if (!frame.isValidCommand())
    {
        Serial.println("错误: 无效的命令帧");
        return;
    }

    switch (frame.command)
    {
    case BT_CMD_SET_DIRECTION: // 0x00
        handleDirectionCommand(BT_DIRECTION_HORIZONTAL);
        Serial.println("设置文本显示方向: 正向显示");
        break;

    case BT_CMD_SET_VERTICAL: // 0x01
        handleDirectionCommand(BT_DIRECTION_VERTICAL);
        Serial.println("设置文本显示方向: 竖向显示");
        break;

    case BT_CMD_SET_FONT_16x16: // 0x02
        Serial.println("设置字体: 16x16");
        resetAllStatesOnFontSwitch(); // 重置所有继承状态
        currentFontSize = BT_FONT_16x16;
        handleTextCommand(upper_text, getUpperTextCharCount(), lower_text, getLowerTextCharCount());
        break;

    case BT_CMD_SET_FONT_32x32: // 0x03
        Serial.println("设置字体: 32x32");
        resetAllStatesOnFontSwitch(); // 重置所有继承状态
        currentFontSize = BT_FONT_32x32;
        handleFullScreenTextCommand(full_text, getFullTextCharCount());
        break;

    case BT_CMD_SET_TEXT: // 0x04
        handleTextCommand(frame);
        break;

    case BT_CMD_SET_ANIMATION: // 0x05
        break;

    case BT_CMD_SET_COLOR: // 0x06
        handleColorCommand(frame);
        break;

    case BT_CMD_SET_BRIGHTNESS: // 0x07
        handleBrightnessCommand(frame);
        break;

    case BT_CMD_SET_EFFECT: // 0x08
        handleEffectCommand(frame);
        break;

    case BT_CMD_SET_BORDER: // 0x09
        handleBorderCommand(frame);
        break;

    case BT_CMD_SET_SPECIFIC_COLOR: // 0x0A
        handleSpecificColorCommand(frame);
        break;

    case BT_CMD_SET_RANDOM_COLOR: // 0x0B
        handleRandomColorCommand(frame);
        break;

    default:
        Serial.printf("未支持的命令: 0x%02X\n", frame.command);
        break;
    }
}

// 处理文本命令 (0x04)
void handleTextCommand(const BluetoothFrame &frame)
{
    // 增加内存安全检查和调试信息
    Serial.printf("收到文本命令 - 帧有效性: %s, 数据长度: %d, 拥有数据: %s\n",
                  frame.isValid ? "是" : "否", frame.dataLength, frame.ownsData ? "是" : "否");

    if (!frame.hasValidData())
    {
        Serial.println("错误: 帧数据无效，跳过处理");
        return;
    }

    if (currentFontSize == BT_FONT_32x32)
    {
        // 32x32字体处理
        uint8_t screenArea;
        int charCount;
        const uint16_t *fontData = frame.getFontData32x32(screenArea, charCount);

        if (fontData && charCount > 0)
        {
            Serial.printf("处理32x32文本命令 - 屏幕区域: 0x%02X, 字符数: %d, 数据指针: %p\n",
                          screenArea, charCount, (void *)fontData);
            handleFullScreenTextCommand(fontData, charCount);
        }
        else
        {
            Serial.printf("错误: 32x32字体数据无效 - 字符数: %d, 数据指针: %p\n", charCount, (void *)fontData);
        }
    }
    else
    {
        // 16x16字体处理
        uint8_t screenArea;
        int charCount;
        const uint16_t *fontData = frame.getFontData16x16(screenArea, charCount);

        if (fontData && charCount > 0)
        {
            Serial.printf("处理16x16文本命令 - 屏幕区域: 0x%02X, 字符数: %d, 数据指针: %p\n",
                          screenArea, charCount, (void *)fontData);

            switch (screenArea)
            {
            case BT_SCREEN_UPPER: // 上半屏
                handleUpperTextCommand(fontData, charCount);
                break;
            case BT_SCREEN_LOWER: // 下半屏
                handleLowerTextCommand(fontData, charCount);
                break;
            case BT_SCREEN_BOTH: // 全屏 (分为上下两部分)
            {
                int halfCount = charCount / 2;
                const uint16_t *upperData = fontData;
                const uint16_t *lowerData = fontData + (halfCount * 16); // 每个字符16个uint16_t
                Serial.printf("全屏模式拆分 - 上半屏: %d字符, 下半屏: %d字符\n", halfCount, charCount - halfCount);
                handleTextCommand(upperData, halfCount, lowerData, charCount - halfCount);
            }
            break;
            default:
                Serial.printf("错误: 无效的屏幕区域 0x%02X\n", screenArea);
                break;
            }
        }
        else
        {
            Serial.printf("错误: 16x16字体数据无效 - 字符数: %d, 数据指针: %p\n", charCount, (void *)fontData);
        }
    }
}

// 独立处理上半屏文本（保持下半屏不变）
void handleUpperTextCommand(const uint16_t *fontData, int charCount)
{
    if (!fontData || charCount <= 0)
        return;

    Serial.printf("设置上半屏文本 - 字符数: %d\n", charCount);

    // 🔄 重要：设置新上半屏文本时清除上半屏的特定字符颜色
    if (colorState.currentColorMode == COLOR_MODE_SPECIFIC)
    {
        Serial.println("🧹 检测到上半屏文本变更，清除上半屏特定字符颜色");
        clearUpperSpecificColors();
    }

    // 只释放上半屏数据
    if (dynamic_upper_text)
    {
        free(dynamic_upper_text);
        dynamic_upper_text = nullptr;
    }

    // 分配并复制上半屏数据
    int upperDataSize = charCount * 16 * sizeof(uint16_t); // 16x16字体每字符16个uint16_t
    dynamic_upper_text = (uint16_t *)malloc(upperDataSize);
    if (dynamic_upper_text)
    {
        memcpy(dynamic_upper_text, fontData, upperDataSize);
        dynamic_upper_char_count = charCount;
        Serial.printf("上半屏数据已更新: %d字符, %d字节\n", charCount, upperDataSize);
    }
    else
    {
        Serial.println("错误: 上半屏数据内存分配失败");
        dynamic_upper_char_count = 0;
    }

    // 更新显示状态（只重置上半屏索引）
    textState.upperIndex = 0;
    textState.lastSwitchTime = millis();
    textState.needUpdate = true;
}

// 独立处理下半屏文本（保持上半屏不变）
void handleLowerTextCommand(const uint16_t *fontData, int charCount)
{
    if (!fontData || charCount <= 0)
        return;

    Serial.printf("设置下半屏文本 - 字符数: %d\n", charCount);

    // 🔄 重要：设置新下半屏文本时清除下半屏的特定字符颜色
    if (colorState.currentColorMode == COLOR_MODE_SPECIFIC)
    {
        Serial.println("🧹 检测到下半屏文本变更，清除下半屏特定字符颜色");
        clearLowerSpecificColors();
    }

    // 只释放下半屏数据
    if (dynamic_lower_text)
    {
        free(dynamic_lower_text);
        dynamic_lower_text = nullptr;
    }

    // 分配并复制下半屏数据
    int lowerDataSize = charCount * 16 * sizeof(uint16_t); // 16x16字体每字符16个uint16_t
    dynamic_lower_text = (uint16_t *)malloc(lowerDataSize);
    if (dynamic_lower_text)
    {
        memcpy(dynamic_lower_text, fontData, lowerDataSize);
        dynamic_lower_char_count = charCount;
        Serial.printf("下半屏数据已更新: %d字符, %d字节\n", charCount, lowerDataSize);
    }
    else
    {
        Serial.println("错误: 下半屏数据内存分配失败");
        dynamic_lower_char_count = 0;
    }

    // 更新显示状态（只重置下半屏索引）
    textState.lowerIndex = 0;
    textState.lastSwitchTime = millis();
    textState.needUpdate = true;
}

// 处理边框命令 (0x09)
void handleBorderCommand(const BluetoothFrame &frame)
{
    if (!frame.hasValidData() || frame.dataLength < BT_BORDER_DATA_LEN)
    {
        Serial.printf("错误: 边框命令数据无效 - 数据长度: %d, 期望: %d\n",
                      frame.dataLength, BT_BORDER_DATA_LEN);
        return;
    }

    uint8_t style, colorIndex, effect, speed;
    frame.getBorderData(style, colorIndex, effect, speed);

    Serial.printf("🎨 收到边框命令 - 样式:%d, 颜色索引:%d, 效果:%d, 速度:%d\n",
                  style, colorIndex, effect, speed);

    // 验证参数有效性
    if (style > BORDER_STYLE_RAINBOW)
    {
        Serial.printf("错误: 无效的边框样式 %d (有效范围: 0-4)\n", style);
        return;
    }

    if (colorIndex > 6)
    {
        Serial.printf("错误: 无效的颜色索引 %d (有效范围: 0-6)\n", colorIndex);
        return;
    }

    if (effect > 3)
    {
        Serial.printf("错误: 无效的边框效果 %d (有效范围: 0-3)\n", effect);
        return;
    }

    if (speed < 1 || speed > 10)
    {
        Serial.printf("错误: 无效的边框速度 %d (有效范围: 1-10)\n", speed);
        return;
    }

    // 根据边框样式进行逻辑处理
    if (style == BORDER_STYLE_NONE)
    {
        // 无边框：清除边框效果
        clearBorderEffect();
        Serial.println("✅ 边框已清除");
        return;
    }

    // 确定颜色
    uint16_t borderColor = COLOR_WHITE; // 默认颜色
    if (style == BORDER_STYLE_RAINBOW)
    {
        // 彩虹边框：颜色参数无效，使用默认白色（实际会被彩虹色覆盖）
        borderColor = COLOR_WHITE;
        Serial.println("📝 彩虹边框模式：忽略颜色选择参数");
    }
    else
    {
        // 其他边框：使用颜色索引映射
        borderColor = BORDER_COLORS[colorIndex];
        Serial.printf("📝 使用颜色: 0x%04X\n", borderColor);
    }

    // 角落边框的效果限制
    if (style == BORDER_STYLE_CORNER && (effect == 1 || effect == 2))
    {
        // 角落边框不支持流动效果，转为静止显示
        effect = 0;
        Serial.println("📝 角落边框不支持流动效果，已转为静止显示");
    }

    // 应用边框设置
    setBorderEffect(style, borderColor, effect, speed);

    // 输出最终设置信息
    const char *styleNames[] = {"无边框", "实线", "点线", "角落", "彩虹"};
    const char *colorNames[] = {"红", "绿", "蓝", "黄", "紫", "青", "白"};
    const char *effectNames[] = {"静止", "顺时针", "逆时针", "闪烁"};

    Serial.printf("✅ 边框设置完成 - %s边框, %s色, %s效果, 速度%d\n",
                  styleNames[style],
                  (style == BORDER_STYLE_RAINBOW) ? "彩虹" : colorNames[colorIndex],
                  effectNames[effect], speed);
}

// 处理特定字符颜色命令 (0x0A)
void handleSpecificColorCommand(const BluetoothFrame &frame)
{
    if (!frame.hasValidData() || frame.dataLength < BT_SPECIFIC_COLOR_DATA_LEN)
    {
        Serial.printf("错误: 特定字符颜色命令数据无效 - 数据长度: %d, 期望: %d\n",
                      frame.dataLength, BT_SPECIFIC_COLOR_DATA_LEN);
        return;
    }

    uint8_t screenArea = frame.data[0];
    uint8_t charIndex = frame.data[1];
    uint8_t r = frame.data[2];
    uint8_t g = frame.data[3];
    uint8_t b = frame.data[4];

    Serial.printf("🎨 收到特定字符颜色命令 - 区域:%d, 索引:%d, RGB:(%d,%d,%d)\n",
                  screenArea, charIndex, r, g, b);

    // 验证参数有效性 - 32x32字体支持全屏设置
    if (screenArea != BT_SCREEN_UPPER && screenArea != BT_SCREEN_LOWER && screenArea != BT_SCREEN_BOTH)
    {
        Serial.printf("错误: 不支持的屏幕区域 %d (支持上半屏=1, 下半屏=2, 全屏=3)\n", screenArea);
        return;
    }

    // 转换RGB888到RGB565
    uint16_t color = ((r & 0xF8) << 8) | ((g & 0xFC) << 3) | (b >> 3);

    // 设置特定字符颜色
    setSpecificCharColor(screenArea, charIndex, color);

    const char *areaName = (screenArea == BT_SCREEN_UPPER) ? "上半屏" : (screenArea == BT_SCREEN_LOWER) ? "下半屏"
                                                                                                        : "全屏";
    Serial.printf("✅ 特定字符颜色设置完成 - 区域:%s, 索引:%d, 颜色:0x%04X\n",
                  areaName, charIndex, color);
}

// 处理随机颜色命令 (0x0B)
void handleRandomColorCommand(const BluetoothFrame &frame)
{
    if (!frame.hasValidData() || frame.dataLength < BT_RANDOM_COLOR_DATA_LEN)
    {
        Serial.printf("错误: 随机颜色命令数据无效 - 数据长度: %d, 期望: %d\n",
                      frame.dataLength, BT_RANDOM_COLOR_DATA_LEN);
        return;
    }

    uint8_t screenArea = frame.data[0];
    uint8_t mode = frame.data[1];
    uint8_t interval = frame.data[2];
    uint8_t seed = frame.data[3];

    Serial.printf("🎲 收到随机颜色命令 - 区域:%d, 模式:%d, 间隔:%d, 种子:%d\n",
                  screenArea, mode, interval, seed);

    // 验证参数有效性 - 随机颜色仅支持全屏设置
    if (screenArea != BT_SCREEN_BOTH)
    {
        Serial.printf("错误: 随机颜色仅支持全屏设置，收到区域参数: %d (应为3)\n", screenArea);
        return;
    }

    if (mode > RANDOM_COLOR_BRIGHT)
    {
        Serial.printf("错误: 无效的随机颜色模式 %d (有效范围: 0-6)\n", mode);
        return;
    }

    if (interval < 1 || interval > 10)
    {
        Serial.printf("错误: 无效的更新间隔 %d (有效范围: 1-10)\n", interval);
        return;
    }

    // 设置随机颜色模式
    setRandomColorMode(screenArea, mode, interval, seed);

    const char *areaNames[] = {"", "上半屏", "下半屏", "全屏"};
    const char *modeNames[] = {"关闭", "每字符不同", "全体相同", "彩虹色", "暖色系", "冷色系", "高亮度"};

    Serial.printf("✅ 随机颜色设置完成 - %s, %s模式, 间隔%d秒, 种子%d\n",
                  areaNames[screenArea], modeNames[mode], interval, seed);
}
